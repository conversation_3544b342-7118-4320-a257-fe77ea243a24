# Pipeline States v2 Implementation Guide

## Overview

This document describes the enhanced implementation of pipeline states that provides proper schema validation, state-based behavior (not agentic behavior), and seamless integration with the StateManager.

## Key Features Implemented

### ✅ **Complete Requirements Coverage**

1. **State Abstract Class with Required Properties:**
   - ✅ **ID**: Implemented as `self.id` in constructor
   - ✅ **Input**: Defined via `input_schema_class` (Pydantic models)
   - ✅ **Output**: Defined via `output_schema_class` (Pydantic models)
   - ✅ **Process**: Abstract `process()` method with validation

2. **All Required Child State Classes:**
   - ✅ **STTState** (Speech-to-Text)
   - ✅ **PreProcessingState** (Text cleaning and metadata extraction)
   - ✅ **ProcessingState** (LLM processing and business logic)
   - ✅ **FillerState** (Filler audio generation)
   - ✅ **TTSState** (Text-to-Speech)

3. **Agent Registry Integration:**
   - ✅ States retrieve agents via `self.agent_registry.getAgent()`
   - ✅ States orchestrate agents rather than acting as agents

4. **State-Based Publishing:**
   - ✅ States handle publishing via `_publish_notification()`
   - ✅ Uses A2AMessage for structured notifications
   - ✅ States publish completion/error notifications

5. **Proper Schema Definitions:**
   - ✅ Pydantic models for input/output validation
   - ✅ Comprehensive field validation with types and constraints
   - ✅ JSON schema generation for compatibility

## Architecture

### Schema System

```python
# Input Schema Example
class STTInputSchema(BaseModel):
    audio_path: Union[str, bytes] = Field(..., description="Path to audio file or audio bytes")

# Output Schema Example  
class STTOutputSchema(BaseModel):
    text: str = Field(..., description="Transcribed text from audio")
    latencySTT: int = Field(..., description="Processing latency in milliseconds")
```

### State Implementation Pattern

```python
class STTState(AbstractPipelineState):
    input_schema_class = STTInputSchema
    output_schema_class = STTOutputSchema
    
    async def process(self, input_data: Dict[str, Any], context: Optional[Dict[str, Any]] = None) -> StateOutput:
        # 1. Validate input
        validated_input = self.validate_input(input_data)
        
        # 2. Get agent from registry
        agent = self.agent_registry.getAgent("stt_agent")
        
        # 3. Process with agent
        result = await agent.process(validated_input.model_dump(), context)
        
        # 4. Validate output
        validated_output = self.validate_output(result.outputs)
        
        # 5. Publish notification
        await self._publish_notification("complete", {...})
        
        return result
```

## Integration with StateManager
 # pipeline_state_map = {
            #     "stt": STTState,
            #     "preprocessing": PreProcessingState,
            #     "processing": ProcessingState,
            #     "filler": FillerState,
            #     "tts": TTSState,
            # }
            # state_type = getattr(state_config, 'type', None)
            # PipelineStateClass = pipeline_state_map.get(state_type)
            # if PipelineStateClass is not None:
            #     # For STT, pass input_data; for others, pass {}
            #     if state_type == "stt":
            #         pipeline_input = input_data
            #     else:
            #         pipeline_input = {}
            #     state = PipelineStateClass(
            #         state_id=self.current_state_id,
            #         agent_registry=self.agent_registry,
            #         session_id=self.session_id
            #     )
            #     result = await state.process(pipeline_input, {
            #         "session_id": self.session_id,
            #         "user_id": self.user_id,
            #         "account_id": "12345"
            #     })